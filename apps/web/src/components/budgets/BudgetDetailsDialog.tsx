import { useState } from "react";
import {
  Calendar,
  DollarSign,
  Target,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Clock,
  Tag,
} from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { trpc } from "@/utils/trpc";

interface BudgetDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  budget: any;
  onClose: () => void;
}

export function BudgetDetailsDialog({
  open,
  onOpenChange,
  budget,
  onClose,
}: BudgetDetailsDialogProps) {
  // Fetch detailed budget data with spending analysis
  const { data: budgetDetails, isLoading } = trpc.budgets.getById.useQuery(
    { id: budget?._id },
    { enabled: !!budget?._id && open }
  );

  if (!budget) return null;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const formatDate = (date: string | Date) => {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getBudgetStatus = () => {
    if (!budget.isActive) return { status: "inactive", color: "bg-red-100 text-red-800" };
    
    const now = new Date();
    const startDate = new Date(budget.startDate);
    const endDate = new Date(budget.endDate);
    
    if (now < startDate) return { status: "upcoming", color: "bg-blue-100 text-blue-800" };
    if (now > endDate) return { status: "expired", color: "bg-gray-100 text-gray-800" };
    return { status: "active", color: "bg-green-100 text-green-800" };
  };

  const { status, color } = getBudgetStatus();

  const analysis = budgetDetails?.analysis || {
    totalSpent: 0,
    remaining: budget.amount,
    percentageUsed: 0,
    transactionCount: 0,
    isOverBudget: false,
    shouldAlert: false,
  };

  const progressValue = Math.min(analysis.percentageUsed, 100);
  const isOverBudget = analysis.isOverBudget;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="text-2xl">{budget.name}</DialogTitle>
              <DialogDescription className="mt-1">
                Detailed budget analysis and spending breakdown
              </DialogDescription>
            </div>
            <Badge className={color}>{status}</Badge>
          </div>
        </DialogHeader>

        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Budget Overview Cards */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Budget</CardTitle>
                  <Target className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatCurrency(budget.amount)}</div>
                  <p className="text-xs text-muted-foreground capitalize">
                    {budget.period}
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Spent</CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatCurrency(analysis.totalSpent)}</div>
                  <p className="text-xs text-muted-foreground">
                    {analysis.transactionCount} transactions
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Remaining</CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className={`text-2xl font-bold ${isOverBudget ? 'text-red-600' : ''}`}>
                    {formatCurrency(analysis.remaining)}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {isOverBudget ? 'Over budget' : 'Available'}
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Progress</CardTitle>
                  <TrendingDown className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className={`text-2xl font-bold ${isOverBudget ? 'text-red-600' : ''}`}>
                    {analysis.percentageUsed.toFixed(1)}%
                  </div>
                  <p className="text-xs text-muted-foreground">
                    of budget used
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Progress Bar */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Budget Progress</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Spent vs Budget</span>
                    <span className={isOverBudget ? "text-red-600" : "text-muted-foreground"}>
                      {formatCurrency(analysis.totalSpent)} / {formatCurrency(budget.amount)}
                    </span>
                  </div>
                  <Progress 
                    value={progressValue} 
                    className={`h-3 ${isOverBudget ? "bg-red-100" : ""}`}
                  />
                </div>

                {/* Alerts */}
                {isOverBudget && (
                  <Alert className="border-red-200 bg-red-50">
                    <AlertTriangle className="h-4 w-4 text-red-600" />
                    <AlertDescription className="text-red-800">
                      You have exceeded your budget by {formatCurrency(Math.abs(analysis.remaining))}.
                      Consider reviewing your spending or adjusting your budget.
                    </AlertDescription>
                  </Alert>
                )}

                {analysis.shouldAlert && !isOverBudget && (
                  <Alert className="border-yellow-200 bg-yellow-50">
                    <AlertTriangle className="h-4 w-4 text-yellow-600" />
                    <AlertDescription className="text-yellow-800">
                      You've reached {budget.alertThreshold}% of your budget limit. 
                      Monitor your spending to stay on track.
                    </AlertDescription>
                  </Alert>
                )}

                {!analysis.shouldAlert && !isOverBudget && analysis.totalSpent > 0 && (
                  <Alert className="border-green-200 bg-green-50">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <AlertDescription className="text-green-800">
                      Great job! You're staying within your budget limits.
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>

            {/* Budget Details */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Budget Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Calendar className="h-4 w-4" />
                      <span>Period</span>
                    </div>
                    <p className="font-medium capitalize">{budget.period}</p>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <AlertTriangle className="h-4 w-4" />
                      <span>Alert Threshold</span>
                    </div>
                    <p className="font-medium">{budget.alertThreshold}%</p>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Clock className="h-4 w-4" />
                      <span>Start Date</span>
                    </div>
                    <p className="font-medium">{formatDate(budget.startDate)}</p>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Clock className="h-4 w-4" />
                      <span>End Date</span>
                    </div>
                    <p className="font-medium">{formatDate(budget.endDate)}</p>
                  </div>
                </div>

                <Separator />

                {/* Categories */}
                <div className="space-y-3">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Tag className="h-4 w-4" />
                    <span>Tracked Categories</span>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {budget.categoryIds?.map((category: any) => (
                      <Badge key={category._id || category} variant="outline">
                        {category.name || category}
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Time Remaining */}
            {status === 'active' && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Time Remaining</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-4">
                    <div className="text-3xl font-bold text-blue-600">
                      {Math.max(0, Math.ceil((new Date(budget.endDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)))}
                    </div>
                    <p className="text-muted-foreground">days remaining</p>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        )}

        <div className="flex justify-end pt-4">
          <Button onClick={onClose}>Close</Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
